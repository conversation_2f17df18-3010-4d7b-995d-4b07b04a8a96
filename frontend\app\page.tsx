"use client";

import Link from 'next/link';
import { useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { CheckCircle, XCircle, Shield, Zap, TrendingUp, Settings, X, Send } from 'lucide-react';
import { DynamicHeader } from '@/components/dynamic-header';
import RiskLadderComparison from '@/components/risk-ladder-comparison';

export default function LandingPage() {
  // Interactive scroll functionality
  useEffect(() => {
    // Get all text blocks and images
    const textBlocks = [
      document.getElementById('text-block-1'),
      document.getElementById('text-block-2'),
      document.getElementById('text-block-3')
    ];

    const images = [
      document.getElementById('image-1'),
      document.getElementById('image-2'),
      document.getElementById('image-3')
    ];

    // Function to show specific image and hide others
    function showImage(index: number) {
      images.forEach((img, i) => {
        if (img) {
          if (i === index) {
            img.classList.remove('opacity-0');
            img.classList.add('opacity-100');
          } else {
            img.classList.remove('opacity-100');
            img.classList.add('opacity-0');
          }
        }
      });
    }

    // Create intersection observer
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // Get the index of the intersecting text block
          const index = textBlocks.indexOf(entry.target as HTMLElement);
          if (index !== -1) {
            showImage(index);
          }
        }
      });
    }, {
      threshold: 0.5, // Trigger when text block is 50% visible (centered)
      rootMargin: '-40% 0px -40% 0px' // Only trigger when text is in center 20% of viewport
    });

    // Section observer to ensure first image shows when entering section
    const sectionElement = document.getElementById('scroll-section');
    const sectionObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          showImage(0); // Always show first image when section becomes visible
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -40% 0px' // Trigger when section is 40% into viewport
    });

    if (sectionElement) {
      sectionObserver.observe(sectionElement);
    }

    // Observe all text blocks
    textBlocks.forEach(block => {
      if (block) {
        observer.observe(block);
      }
    });

    // Initialize with first image visible
    showImage(0);

    // Cleanup function
    return () => {
      textBlocks.forEach(block => {
        if (block) {
          observer.unobserve(block);
        }
      });
      if (sectionElement) {
        sectionObserver.unobserve(sectionElement);
      }
    };
  }, []);

  return (
    <div className="min-h-screen text-white">
      {/* Dynamic Header */}
      <DynamicHeader />

      {/* Hero Section */}
      <section id="hero" className="min-h-screen flex items-center justify-center pt-60 relative overflow-hidden px-6">
        <div className="w-full max-w-7xl mx-auto">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-12 lg:gap-16">
            {/* Left Column - Hero Content */}
            <div className="flex-1 text-left">
              <h1 className="text-[32px] sm:text-[40px] md:text-[48px] lg:text-[58px] font-semibold font-figtree mb-6 gradient-text-primary-to-white tracking-tighter">
                <span className="block leading-none mb-1">We Find Pump.fun Winners,</span>
                <span className="block leading-none">You Time the Exits</span>
              </h1>
              <p className="text-[19px] text-[#B0B0B0] font-light font-figtree mb-8 max-w-2xl tracking-tight leading-[23px]">
                Stop gambling on random launches. Our predictive models filter out 90%+ of the noise, giving you a statistical edge to catch profitable memecoins before the crowd.
              </p>
              <Button
                variant="hero"
                size="lg"
                className="px-12 py-6 text-xl hover:shadow-[0_0_10px_#00d4ff] transition-all duration-300"
              >
                <Link href="/dashboard">Launch App</Link>
              </Button>
            </div>

            {/* Right Column - Performance Comparison Tables */}
            <div className="flex-1 flex justify-center lg:justify-end">
              <RiskLadderComparison />
            </div>
          </div>
        </div>
      </section>

      {/* ML vs Manual Filters Section */}
      <section className="min-h-screen py-20 px-6 relative overflow-hidden flex items-center justify-center">
        <div className="container mx-auto max-w-7xl relative z-20">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-semibold font-figtree mb-20 tracking-tighter leading-tight text-center text-white">
            <span className="block">Others Make You Guess.</span>
            <span className="block">We Let AI Decide.</span>
          </h2>

          <div className="grid lg:grid-cols-5 gap-6 lg:gap-8 items-center mb-20">
            {/* Competitors Side */}
            <div className="lg:col-span-2 glass-card glass-card-hover rounded-2xl p-8 border-2 border-red-500/30 bg-red-500/5 relative overflow-hidden min-h-[500px]">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold font-figtree text-red-400 mb-2 tracking-tighter">
                  Competitors
                </h3>
                <p className="text-[#B0B0B0] font-light font-figtree tracking-tight mb-2">
                  Manual Filter Tweaking
                </p>
                <p className="text-sm text-red-300 font-figtree tracking-tight">
                  Endless tweaking, mediocre results
                </p>
              </div>

              <div className="grid grid-cols-2 gap-3 mb-8">
                <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3 text-center text-sm text-red-300 animate-pulse">
                  <div className="text-lg mb-1">⚙️</div>
                  Liquidity &gt; $10K
                </div>
                <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3 text-center text-sm text-red-300 animate-pulse" style={{animationDelay: '0.5s'}}>
                  <div className="text-lg mb-1">⚙️</div>
                  Wallet Count &gt; 50
                </div>
                <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3 text-center text-sm text-red-300 animate-pulse" style={{animationDelay: '1s'}}>
                  <div className="text-lg mb-1">⚙️</div>
                  Social Score &gt; 3
                </div>
                <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3 text-center text-sm text-red-300 animate-pulse" style={{animationDelay: '1.5s'}}>
                  <div className="text-lg mb-1">⚙️</div>
                  Dev Tokens &lt; 20%
                </div>
                <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3 text-center text-sm text-red-300 animate-pulse" style={{animationDelay: '2s'}}>
                  <div className="text-lg mb-1">⚙️</div>
                  Volume &gt; $50K
                </div>
                <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3 text-center text-sm text-red-300 animate-pulse" style={{animationDelay: '2.5s'}}>
                  <div className="text-lg mb-1">⚙️</div>
                  Holder Growth &gt; 2%
                </div>
              </div>

              <div className="text-center mt-6">
                <div className="text-4xl">🤔</div>
              </div>
            </div>

            {/* VS Divider */}
            <div className="flex items-center justify-center lg:order-none order-first lg:mb-0 mb-8">
              <div className="w-20 h-20 bg-gradient-to-r from-red-500 to-primary rounded-full flex items-center justify-center text-xl font-black text-white animate-spin" style={{animationDuration: '4s'}}>
                VS
              </div>
            </div>

            {/* Our Side */}
            <div className="lg:col-span-2 glass-card glass-card-hover rounded-2xl p-8 border-2 border-primary/30 bg-primary/5 relative overflow-hidden min-h-[500px]">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold font-figtree text-primary mb-2 tracking-tighter">
                  Our Platform
                </h3>
                <p className="text-[#B0B0B0] font-light font-figtree tracking-tight mb-2">
                  AI-Powered Predictions
                </p>
                <p className="text-sm text-primary font-figtree tracking-tight">
                  AI finds patterns you can't
                </p>
              </div>

              <div className="flex flex-col items-center justify-center h-[300px]">
                <div className="grid grid-cols-4 gap-2 mb-6 w-full">
                  <div className="bg-primary/10 border border-primary/30 rounded-md p-2 text-center text-xs text-primary animate-bounce" style={{animationDelay: '0s'}}>
                    Liquidity
                  </div>
                  <div className="bg-primary/10 border border-primary/30 rounded-md p-2 text-center text-xs text-primary animate-bounce" style={{animationDelay: '0.2s'}}>
                    Wallets
                  </div>
                  <div className="bg-primary/10 border border-primary/30 rounded-md p-2 text-center text-xs text-primary animate-bounce" style={{animationDelay: '0.4s'}}>
                    Social
                  </div>
                  <div className="bg-primary/10 border border-primary/30 rounded-md p-2 text-center text-xs text-primary animate-bounce" style={{animationDelay: '0.6s'}}>
                    Dev %
                  </div>
                  <div className="bg-primary/10 border border-primary/30 rounded-md p-2 text-center text-xs text-primary animate-bounce" style={{animationDelay: '0.8s'}}>
                    Volume
                  </div>
                  <div className="bg-primary/10 border border-primary/30 rounded-md p-2 text-center text-xs text-primary animate-bounce" style={{animationDelay: '1s'}}>
                    Growth
                  </div>
                  <div className="bg-primary/10 border border-primary/30 rounded-md p-2 text-center text-xs text-primary animate-bounce" style={{animationDelay: '1.2s'}}>
                    Timing
                  </div>
                  <div className="bg-primary/10 border border-primary/30 rounded-md p-2 text-center text-xs text-primary animate-bounce" style={{animationDelay: '1.4s'}}>
                    Sentiment
                  </div>
                  <div className="bg-primary/10 border border-primary/30 rounded-md p-2 text-center text-xs text-primary animate-bounce" style={{animationDelay: '1.6s'}}>
                    Velocity
                  </div>
                  <div className="bg-primary/10 border border-primary/30 rounded-md p-2 text-center text-xs text-primary animate-bounce" style={{animationDelay: '1.8s'}}>
                    Patterns
                  </div>
                  <div className="bg-primary/10 border border-primary/30 rounded-md p-2 text-center text-xs text-primary animate-bounce" style={{animationDelay: '2s'}}>
                    Holders
                  </div>
                  <div className="bg-primary/10 border border-primary/30 rounded-md p-2 text-center text-xs text-primary animate-bounce" style={{animationDelay: '2.2s'}}>
                    +100s more
                  </div>
                </div>

                <div className="w-20 h-20 bg-gradient-to-r from-primary to-blue-400 rounded-full flex items-center justify-center text-2xl mb-4 animate-pulse">
                  🧠
                </div>
                <div className="text-2xl text-primary mb-4">↓</div>
                <div className="bg-gradient-to-r from-primary to-blue-400 rounded-lg px-6 py-3 font-bold text-black text-center">
                  Buy Signal
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* From Research Paralysis to Profit Timing Section */}
      <section className="min-h-screen py-20 px-6 relative overflow-hidden flex items-center justify-center">
        {/* Background Elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-10 pointer-events-none">
          <div className="absolute top-[10%] left-[10%] text-5xl opacity-10 animate-float-1">📊</div>
          <div className="absolute top-[20%] right-[15%] text-4xl opacity-10 animate-float-2">🚀</div>
          <div className="absolute bottom-[30%] left-[20%] text-5xl opacity-10 animate-float-3">💎</div>
          <div className="absolute bottom-[15%] right-[10%] text-6xl opacity-10 animate-float-4">⚡</div>
        </div>

        <div className="container mx-auto max-w-7xl relative z-20">
          {/* Main Title */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-semibold font-figtree mb-6 tracking-tighter leading-tight animate-title-glow text-white">
              From Research Paralysis to Profit Timing
            </h2>
            <p className="text-lg text-[#B0B0B0] font-light font-figtree max-w-3xl mx-auto mb-8 tracking-tight leading-relaxed">
              Every day, thousands of new tokens launch. You're drowning in Discord and Telegram, checking rushed websites and X accounts, and trying to decode if 'DiamondHands420' is legit—all while the window closes.
            </p>
          </div>

          {/* Two Column Comparison */}
          <div className="grid md:grid-cols-2 gap-8 mb-16">
            {/* Before: Research Roulette */}
            <div className="glass-card glass-card-hover rounded-2xl p-8 border-l-4 border-red-500/50 bg-red-500/5 relative overflow-hidden group">
              {/* Shimmer Effect */}
              <div className="absolute top-0 left-[-100%] w-full h-full bg-gradient-to-r from-transparent via-white/10 to-transparent transition-all duration-500 group-hover:left-[100%]"></div>

              <div className="mb-6 relative z-10">
                <div className="flex items-center gap-3 mb-2">
                  <span className="text-3xl">⚠️</span>
                  <h3 className="text-2xl font-bold font-figtree text-red-400 tracking-tighter">
                    Before: Research Roulette
                  </h3>
                </div>
              </div>
              <div className="space-y-4 relative z-10">
                <div className="flex items-center gap-3 py-3 border-b border-white/10">
                  <span className="text-xl text-red-400 flex-shrink-0">🔍</span>
                  <p className="text-[#B0B0B0] font-figtree tracking-tight text-sm leading-relaxed">
                    Scanning 1000s of launches daily
                  </p>
                </div>
                <div className="flex items-center gap-3 py-3 border-b border-white/10">
                  <span className="text-xl text-red-400 flex-shrink-0">❌</span>
                  <p className="text-[#B0B0B0] font-figtree tracking-tight text-sm leading-relaxed">
                    Missing opportunities while you research
                  </p>
                </div>
                <div className="flex items-center gap-3 py-3">
                  <span className="text-xl text-red-400 flex-shrink-0">🎲</span>
                  <p className="text-[#B0B0B0] font-figtree tracking-tight text-sm leading-relaxed">
                    90% chance of picking a rug anyway
                  </p>
                </div>
              </div>
            </div>

            {/* After: Early + Informed + Exit Mastery */}
            <div className="glass-card glass-card-hover rounded-2xl p-8 border-l-4 border-green-500/50 bg-green-500/5 relative overflow-hidden group">
              {/* Shimmer Effect */}
              <div className="absolute top-0 left-[-100%] w-full h-full bg-gradient-to-r from-transparent via-white/10 to-transparent transition-all duration-500 group-hover:left-[100%]"></div>

              <div className="mb-6 relative z-10">
                <div className="flex items-center gap-3 mb-2">
                  <span className="text-3xl">✅</span>
                  <h3 className="text-2xl font-bold font-figtree text-green-400 tracking-tighter">
                    After: Clear Entry Signal, Your Exit Mastery
                  </h3>
                </div>
              </div>
              <div className="space-y-4 relative z-10">
                <div className="flex items-center gap-3 py-3 border-b border-white/10">
                  <span className="text-xl text-green-400 flex-shrink-0">🎯</span>
                  <p className="text-[#B0B0B0] font-figtree tracking-tight text-sm leading-relaxed">
                    Get in early on our filtered picks
                  </p>
                </div>
                <div className="flex items-center gap-3 py-3 border-b border-white/10">
                  <span className="text-xl text-green-400 flex-shrink-0">💪</span>
                  <p className="text-[#B0B0B0] font-figtree tracking-tight text-sm leading-relaxed">
                    Research from a position of strength
                  </p>
                </div>
                <div className="flex items-center gap-3 py-3">
                  <span className="text-xl text-green-400 flex-shrink-0">💪</span>
                  <p className="text-[#B0B0B0] font-figtree tracking-tight text-sm leading-relaxed">
                    Research from a position of strength
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* From Signal to Profit Section */}
      <section id="scroll-section" className="py-20 px-6">
        <div className="container mx-auto max-w-7xl">
          {/* Section Title */}
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-semibold font-figtree mb-6 tracking-tighter leading-tight text-white">
              From Signal to Profit in 3 Steps
            </h2>
          </div>

          {/* Mobile Layout (Stacked) */}
          <div className="md:hidden space-y-12">
            {/* Mobile Block 1 */}
            <div className="text-center">
              <img src="https://picsum.photos/seed/image1/800/600" alt="Set Trading Parameters" className="w-full h-64 object-cover rounded-lg mb-6" />
              <h2 className="text-3xl font-bold font-figtree text-white mb-4 tracking-tighter">Set Trading Parameters & Start Bot</h2>
              <p className="text-lg text-[#B0B0B0] font-figtree leading-relaxed tracking-tight">Configure your buy amount, risk level, and trading preferences. Hit 'Start Bot' and let our algorithm scan the market 24/7.</p>
            </div>

            {/* Mobile Block 2 */}
            <div className="text-center">
              <img src="https://picsum.photos/seed/image2/800/600" alt="Wait for Buy Signal" className="w-full h-64 object-cover rounded-lg mb-6" />
              <h2 className="text-3xl font-bold font-figtree text-white mb-4 tracking-tighter">Wait for Buy Signal & Confirm Purchase</h2>
              <p className="text-lg text-[#B0B0B0] font-figtree leading-relaxed tracking-tight">When our AI models identify a high-probability winner, you'll get a buy proposal. Review and confirm the purchase.</p>
            </div>

            {/* Mobile Block 3 */}
            <div className="text-center">
              <img src="https://picsum.photos/seed/image3/800/600" alt="Research & Time Your Exit" className="w-full h-64 object-cover rounded-lg mb-6" />
              <h2 className="text-3xl font-bold font-figtree text-white mb-4 tracking-tighter">Research & Time Your Exit</h2>
              <p className="text-lg text-[#B0B0B0] font-figtree leading-relaxed tracking-tight">Now apply your trading skills where they matter most. Analyze the token's price action, research the fundamentals, socials, groups and decide when to sell for maximum profit.</p>
            </div>
          </div>

          {/* Desktop Layout (Side-by-side with scroll interaction) */}
          <div className="hidden md:grid md:grid-cols-2 gap-12">
            {/* Left Column - Sticky Images - Vertically Centered */}
            <div className="sticky top-0 h-screen flex items-center justify-center">
              <div className="relative w-full max-w-lg">
                <img id="image-1" src="https://picsum.photos/seed/image1/800/600" alt="Set Trading Parameters" className="w-full h-96 object-cover rounded-lg transition-opacity duration-500" />
                <img id="image-2" src="https://picsum.photos/seed/image2/800/600" alt="Wait for Buy Signal" className="w-full h-96 object-cover rounded-lg absolute top-0 left-0 opacity-0 transition-opacity duration-500" />
                <img id="image-3" src="https://picsum.photos/seed/image3/800/600" alt="Research & Time Your Exit" className="w-full h-96 object-cover rounded-lg absolute top-0 left-0 opacity-0 transition-opacity duration-500" />
              </div>
            </div>

            {/* Right Column - Scrolling Text Blocks */}
            <div className="relative">
              {/* Text Block 1 - Positioned to align with centered image */}
              <div id="text-block-1" className="h-screen flex items-center">
                <div className="w-full">
                  <h2 className="text-4xl lg:text-5xl font-bold font-figtree text-white mb-6 tracking-tighter">Set Trading Parameters & Start Bot</h2>
                  <p className="text-xl text-[#B0B0B0] font-figtree leading-relaxed mb-6 tracking-tight">
                    Configure your buy amount, risk level, and trading preferences. Hit 'Start Bot' and let our algorithm scan the market 24/7.
                  </p>
                  <p className="text-lg text-[#808080] font-figtree leading-relaxed tracking-tight">
                    Set your maximum investment per token, choose your risk tolerance, and define your trading strategy. Our bot will continuously monitor the market, applying our AI models to identify high-potential opportunities while you focus on other activities.
                  </p>
                </div>
              </div>

              {/* Text Block 2 - Positioned to align with centered image */}
              <div id="text-block-2" className="h-screen flex items-center">
                <div className="w-full">
                  <h2 className="text-4xl lg:text-5xl font-bold font-figtree text-white mb-6 tracking-tighter">Wait for Buy Signal & Confirm Purchase</h2>
                  <p className="text-xl text-[#B0B0B0] font-figtree leading-relaxed mb-6 tracking-tight">
                    When our AI models identify a high-probability winner, you'll get a buy proposal. Review and confirm the purchase.
                  </p>
                  <p className="text-lg text-[#808080] font-figtree leading-relaxed tracking-tight">
                    Receive instant notifications when our predictive models detect tokens with strong profit potential. Each signal includes key metrics, confidence scores, and reasoning. You maintain full control—every purchase requires your approval.
                  </p>
                </div>
              </div>

              {/* Text Block 3 - Positioned to align with centered image */}
              <div id="text-block-3" className="h-screen flex items-center">
                <div className="w-full">
                  <h2 className="text-4xl lg:text-5xl font-bold font-figtree text-white mb-6 tracking-tighter">Research & Time Your Exit</h2>
                  <p className="text-xl text-[#B0B0B0] font-figtree leading-relaxed mb-6 tracking-tight">
                    Now apply your trading skills where they matter most. Analyze the token's price action, research the fundamentals, socials, groups and decide when to sell for maximum profit.
                  </p>
                  <p className="text-lg text-[#808080] font-figtree leading-relaxed tracking-tight">
                    With your position secured early, dive deep into the project's community, roadmap, and market dynamics. Use your analytical skills to determine optimal exit points—whether that's a quick 3x gain or holding for potential moonshot returns.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faq" className="py-20 px-6">
        <div className="container mx-auto max-w-4xl">
          <h2 className="text-4xl font-bold font-figtree text-center mb-16 text-white tracking-tighter leading-[2.25rem]">
            Frequently Asked Questions
          </h2>
          <Accordion type="single" collapsible className="space-y-4">
            {[
              {
                question: "Quid est Lorem Ipsum?",
                answer: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."
              },
              {
                question: "Unde venit Lorem Ipsum?",
                answer: "Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
              },
              {
                question: "Cur Lorem Ipsum utimur?",
                answer: "Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo."
              },
              {
                question: "Ubi Lorem Ipsum invenire possum?",
                answer: "Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt."
              },
              {
                question: "Estne Lorem Ipsum tutum?",
                answer: "Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem."
              }
            ].map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`} className="glass-card border-none">
                <AccordionTrigger className="px-6 py-4 text-white font-figtree hover:no-underline hover:text-primary tracking-tight leading-[1.2]">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-4 text-[#B0B0B0] font-figtree tracking-tight leading-[1.2]">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 px-6">
        <div className="container mx-auto max-w-4xl">
          {/* New Animated CTA Section */}
          <div className="flex justify-center mb-12">
            <div className="relative max-w-2xl w-full">
              {/* Animated gradient border */}
              <div className="absolute inset-0 bg-gradient-to-r from-red-500 via-primary to-green-400 rounded-3xl animate-gradient-shift p-0.5">
                <div className="w-full h-full bg-gradient-to-br from-black/95 to-[#1a1a2e]/95 rounded-3xl"></div>
              </div>

              {/* Content */}
              <div className="relative z-10 p-12 text-center">
                <div className="text-4xl md:text-5xl font-bold mb-6 text-white leading-tight">
                  Stop Wasting Alpha on Coin Selection
                </div>

                <div className="flex items-center justify-center my-8 gap-4">
                  <div className="flex-1 h-0.5 bg-gradient-to-r from-transparent via-primary to-transparent"></div>
                  <div className="text-4xl text-primary animate-bounce">⚡</div>
                  <div className="flex-1 h-0.5 bg-gradient-to-r from-transparent via-primary to-transparent"></div>
                </div>

                <div className="text-3xl md:text-4xl font-semibold text-green-400" style={{textShadow: '0 0 20px rgba(52, 211, 153, 0.3)'}}>
                  Start Maximizing Profits Instead
                </div>
              </div>
            </div>
          </div>

          {/* Launch App Button */}
          <div className="text-center">
            <Button
              variant="hero"
              size="lg"
              className="px-16 py-8 text-2xl hover:shadow-[0_0_20px_#00d4ff] transition-all duration-300"
            >
              <Link href="/dashboard">Launch App</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-white/10 py-8 px-6">
        <div className="container mx-auto max-w-6xl flex items-center justify-between">
          <div className="text-[#B0B0B0] font-figtree tracking-tight leading-[1.2]">
            © 2024 ScryBot. All rights reserved.
          </div>
          <div className="flex items-center gap-4">
            <a href="#" className="text-[#B0B0B0] hover:text-primary transition-colors">
              <X className="h-5 w-5" />
            </a>
            <a href="#" className="text-[#B0B0B0] hover:text-primary transition-colors">
              <Send className="h-5 w-5" />
            </a>
          </div>
        </div>
      </footer>
    </div>
  );
}
